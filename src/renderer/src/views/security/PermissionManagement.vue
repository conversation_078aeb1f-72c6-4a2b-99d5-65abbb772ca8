<template>
  <div class="permission-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回企业资源管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Shield class="title-icon" />
            权限管理
          </h1>
          <p class="page-description">管理用户角色、权限分配和访问控制策略</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          @click="showRoleModal"
        >
          <Plus class="btn-icon" />
          添加角色
        </button>
        <button
          class="btn btn-primary"
          @click="showUserModal"
        >
          <UserPlus class="btn-icon" />
          添加用户
        </button>
      </div>
    </div>

    <!-- 权限概览 -->
    <div class="permission-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总用户数"
              :value="users.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Users class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃用户"
              :value="activeUsers"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <UserCheck class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="角色数量"
              :value="roles.length"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <Crown class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="权限项目"
              :value="permissions.length"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <Key class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-tabs
        v-model:activeKey="activeTab"
        type="card"
      >
        <a-tab-pane
          key="users"
          tab="用户管理"
        >
          <a-row :gutter="16">
            <!-- 用户管理 -->
            <a-col :span="12">
              <a-card
                title="用户管理"
                class="users-card"
              >
                <template #extra>
                  <a-input-search
                    v-model:value="userSearchKeyword"
                    placeholder="搜索用户"
                    style="width: 200px"
                    class="custom-search-input"
                    @search="handleUserSearch"
                    @input="handleUserSearch"
                  />
                </template>
                <div class="users-list">
                  <div
                    v-for="user in filteredUsers"
                    :key="user.id"
                    class="user-item"
                    :class="{ active: selectedUser?.id === user.id }"
                    @click="selectUser(user)"
                  >
                    <div class="user-avatar">
                      <a-avatar :size="40">
                        {{ user.username.charAt(0).toUpperCase() }}
                      </a-avatar>
                    </div>
                    <div class="user-info">
                      <div class="user-name">{{ user.username }}</div>
                      <div class="user-email">{{ user.email }}</div>
                      <div class="user-role">
                        <a-tag :color="getRoleColor(user.role)">{{ getRoleLabel(user.role) }}</a-tag>
                      </div>
                    </div>
                    <div class="user-status">
                      <a-badge
                        :status="user.is_active ? 'success' : 'default'"
                        :text="user.is_active ? '活跃' : '禁用'"
                      />
                    </div>
                    <div class="user-actions">
                      <a-dropdown>
                        <a-button
                          type="text"
                          size="small"
                        >
                          <MoreHorizontal class="action-icon" />
                        </a-button>
                        <template #overlay>
                          <a-menu>
                            <a-menu-item @click="editUser(user)">
                              <Edit class="menu-icon" />
                              编辑
                            </a-menu-item>
                            <a-menu-item @click="resetPassword(user)">
                              <RefreshCw class="menu-icon" />
                              重置密码
                            </a-menu-item>
                            <a-menu-divider />
                            <a-menu-item @click="toggleUserStatus(user)">
                              <Power class="menu-icon" />
                              {{ user.is_active ? '禁用' : '启用' }}
                            </a-menu-item>
                            <a-menu-item
                              class="danger-item"
                              @click="deleteUser(user)"
                            >
                              <Trash2 class="menu-icon" />
                              删除
                            </a-menu-item>
                          </a-menu>
                        </template>
                      </a-dropdown>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 角色权限管理 -->
            <a-col :span="12">
              <a-card
                title="角色权限"
                class="roles-card"
              >
                <template #extra>
                  <a-select
                    v-model:value="selectedRoleId"
                    placeholder="选择角色"
                    style="width: 150px"
                    @change="handleRoleChange"
                  >
                    <a-select-option
                      v-for="role in roles"
                      :key="role.id"
                      :value="role.id"
                    >
                      {{ role.name }}
                    </a-select-option>
                  </a-select>
                </template>
                <div
                  v-if="selectedRole"
                  class="role-permissions"
                >
                  <div class="role-info">
                    <h3 class="role-name">{{ selectedRole.name }}</h3>
                    <p class="role-description">{{ selectedRole.description }}</p>
                  </div>
                  <div class="permissions-grid">
                    <div
                      v-for="category in permissionCategories"
                      :key="category.key"
                      class="permission-category"
                    >
                      <div class="category-header">
                        <h4 class="category-title">{{ category.title }}</h4>
                        <a-checkbox
                          :checked="isCategoryFullySelected(category.key)"
                          :indeterminate="isCategoryPartiallySelected(category.key)"
                          @change="toggleCategoryPermissions(category.key)"
                        >
                          全选
                        </a-checkbox>
                      </div>
                      <div class="permission-items">
                        <a-checkbox
                          v-for="permission in category.permissions"
                          :key="permission.key"
                          :checked="hasPermission(permission.key)"
                          @change="togglePermission(permission.key)"
                        >
                          {{ permission.label }}
                        </a-checkbox>
                      </div>
                    </div>
                  </div>
                  <div class="role-actions">
                    <a-button
                      type="primary"
                      @click="saveRolePermissions"
                    >
                      保存权限
                    </a-button>
                    <a-button @click="resetRolePermissions"> 重置 </a-button>
                  </div>
                </div>
                <div
                  v-else
                  class="no-role-selected"
                >
                  <Crown class="no-role-icon" />
                  <p>请选择一个角色来管理权限</p>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 安全事件监控 -->
        <a-tab-pane
          key="security"
          tab="安全事件"
        >
          <div class="security-events">
            <div class="events-header">
              <div class="header-left">
                <h3>安全事件监控</h3>
                <p>实时监控系统安全事件和异常行为</p>
              </div>
              <div class="header-actions">
                <a-select
                  v-model:value="eventFilter"
                  placeholder="事件类型"
                  style="width: 150px; margin-right: 8px"
                >
                  <a-select-option value="all">全部事件</a-select-option>
                  <a-select-option value="login">登录事件</a-select-option>
                  <a-select-option value="permission">权限变更</a-select-option>
                  <a-select-option value="security">安全警告</a-select-option>
                </a-select>
                <a-button @click="refreshSecurityEvents">
                  <RefreshCw class="btn-icon" />
                  刷新
                </a-button>
              </div>
            </div>

            <div class="events-list">
              <div
                v-for="event in filteredSecurityEvents"
                :key="event.id"
                class="event-item"
                :class="event.severity"
              >
                <div class="event-icon">
                  <component :is="getEventIcon(event.type)" />
                </div>
                <div class="event-content">
                  <div class="event-title">{{ event.title }}</div>
                  <div class="event-description">{{ event.description }}</div>
                  <div class="event-meta">
                    <span class="event-time">{{ formatTime(event.timestamp) }}</span>
                    <span class="event-user">{{ event.user }}</span>
                    <span class="event-ip">{{ event.ip_address }}</span>
                  </div>
                </div>
                <div class="event-actions">
                  <a-button
                    size="small"
                    @click="viewEventDetails(event)"
                  >
                    详情
                  </a-button>
                  <a-button
                    size="small"
                    @click="markEventAsHandled(event)"
                  >
                    标记已处理
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 审计日志 -->
        <a-tab-pane
          key="audit"
          tab="审计日志"
        >
          <div class="audit-logs">
            <div class="logs-header">
              <div class="header-left">
                <h3>审计日志</h3>
                <p>查看系统操作记录和用户行为日志</p>
              </div>
              <div class="header-actions">
                <a-range-picker
                  v-model:value="auditDateRange"
                  style="margin-right: 8px"
                  @change="handleDateRangeChange"
                />
                <a-select
                  v-model:value="auditLogLevel"
                  placeholder="日志级别"
                  style="width: 120px; margin-right: 8px"
                >
                  <a-select-option value="all">全部</a-select-option>
                  <a-select-option value="info">信息</a-select-option>
                  <a-select-option value="warning">警告</a-select-option>
                  <a-select-option value="error">错误</a-select-option>
                </a-select>
                <a-button @click="exportAuditLogs">
                  <Download class="btn-icon" />
                  导出
                </a-button>
              </div>
            </div>

            <a-table
              :columns="auditColumns"
              :data-source="filteredAuditLogs"
              :pagination="{ pageSize: 20 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'level'">
                  <a-tag :color="getLogLevelColor(record.level)">
                    {{ record.level.toUpperCase() }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-button
                    size="small"
                    @click="viewLogDetails(record)"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 权限配置 -->
        <a-tab-pane
          key="permissions"
          tab="权限配置"
        >
          <div class="permission-config">
            <div class="config-header">
              <div class="header-left">
                <h3>权限配置</h3>
                <p>配置系统权限项目和访问控制策略</p>
              </div>
              <div class="header-actions">
                <a-button @click="addPermission">
                  <Plus class="btn-icon" />
                  添加权限
                </a-button>
              </div>
            </div>

            <div class="permission-tree">
              <a-tree
                v-model:checkedKeys="checkedPermissions"
                :tree-data="permissionTreeData"
                checkable
                :default-expand-all="true"
                @check="handlePermissionCheck"
              >
                <template #title="{ title, key, description }">
                  <div class="permission-node">
                    <span class="permission-title">{{ title }}</span>
                    <span class="permission-desc">{{ description }}</span>
                  </div>
                </template>
              </a-tree>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 用户详情面板 -->
    <div
      v-if="selectedUser"
      class="user-details"
    >
      <a-card
        title="用户详情"
        class="details-card"
      >
        <template #extra>
          <a-button @click="closeUserDetails">
            <X class="btn-icon" />
            关闭
          </a-button>
        </template>
        <div class="user-profile">
          <div class="profile-header">
            <a-avatar :size="64">
              {{ selectedUser.username.charAt(0).toUpperCase() }}
            </a-avatar>
            <div class="profile-info">
              <h3 class="profile-name">{{ selectedUser.username }}</h3>
              <p class="profile-email">{{ selectedUser.email }}</p>
              <a-tag :color="getRoleColor(selectedUser.role)">{{ getRoleLabel(selectedUser.role) }}</a-tag>
            </div>
          </div>
          <div class="profile-details">
            <a-descriptions
              :column="2"
              size="small"
            >
              <a-descriptions-item label="用户ID">{{ selectedUser.id }}</a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-badge
                  :status="selectedUser.is_active ? 'success' : 'default'"
                  :text="selectedUser.is_active ? '活跃' : '禁用'"
                />
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ formatTime(selectedUser.created_at) }}</a-descriptions-item>
              <a-descriptions-item label="最后登录">{{ formatTime(selectedUser.last_login_at) }}</a-descriptions-item>
              <a-descriptions-item label="LDAP DN">{{ selectedUser.ldap_dn || '未配置' }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <div class="user-permissions">
            <h4>用户权限</h4>
            <div class="permission-tags">
              <a-tag
                v-for="permission in getUserPermissions(selectedUser)"
                :key="permission"
                color="blue"
              >
                {{ getPermissionLabel(permission) }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <a-modal
      v-model:open="showUserModalVisible"
      :title="editingUser ? '编辑用户' : '添加用户'"
      width="600px"
      @ok="handleSaveUser"
      @cancel="handleCancelUser"
    >
      <a-form
        ref="userForm"
        :model="userFormData"
        :rules="userFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="用户名"
              name="username"
            >
              <a-input
                v-model:value="userFormData.username"
                placeholder="请输入用户名"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="邮箱"
              name="email"
            >
              <a-input
                v-model:value="userFormData.email"
                placeholder="请输入邮箱"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="角色"
              name="role"
            >
              <a-select
                v-model:value="userFormData.role"
                placeholder="请选择角色"
              >
                <a-select-option value="personal">个人用户</a-select-option>
                <a-select-option value="enterprise_admin">企业管理员</a-select-option>
                <a-select-option value="system_admin">系统管理员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="userFormData.is_active"> 启用用户 </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          label="LDAP DN"
          name="ldap_dn"
        >
          <a-input
            v-model:value="userFormData.ldap_dn"
            placeholder="cn=user,ou=users,dc=company,dc=com"
          />
        </a-form-item>
        <a-form-item
          v-if="!editingUser"
          label="密码"
          name="password"
        >
          <a-input-password
            v-model:value="userFormData.password"
            placeholder="请输入密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加/编辑角色模态框 -->
    <a-modal
      v-model:open="showRoleModalVisible"
      :title="editingRole ? '编辑角色' : '添加角色'"
      width="500px"
      @ok="handleSaveRole"
      @cancel="handleCancelRole"
    >
      <a-form
        ref="roleForm"
        :model="roleFormData"
        :rules="roleFormRules"
        layout="vertical"
      >
        <a-form-item
          label="角色名称"
          name="name"
        >
          <a-input
            v-model:value="roleFormData.name"
            placeholder="请输入角色名称"
          />
        </a-form-item>
        <a-form-item
          label="角色描述"
          name="description"
        >
          <a-textarea
            v-model:value="roleFormData.description"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, notification } from 'ant-design-vue'
import eventBus from '@/utils/eventBus'
import { Shield, Plus, UserPlus, Users, UserCheck, Crown, Key, MoreHorizontal, Edit, RefreshCw, Power, Trash2, X, ArrowLeft } from 'lucide-vue-next'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 权限管理页面
 * 功能：管理用户角色、权限分配和访问控制策略
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 路由实例
const router = useRouter()

// 响应式数据
const activeTab = ref('users')
const userSearchKeyword = ref('')
const selectedUser = ref(null)
const selectedRoleId = ref('')
const showUserModalVisible = ref(false)
const showRoleModalVisible = ref(false)
const editingUser = ref(null)
const editingRole = ref(null)

// 安全事件相关
const eventFilter = ref('all')
const securityEvents = ref([
  {
    id: 1,
    type: 'login',
    severity: 'info',
    title: '用户登录成功',
    description: '用户 admin 从 ************* 成功登录系统',
    user: 'admin',
    ip_address: '*************',
    timestamp: new Date('2025-01-15T10:30:00Z'),
    handled: false
  },
  {
    id: 2,
    type: 'permission',
    severity: 'warning',
    title: '权限变更',
    description: '用户 john.doe 的角色从个人用户变更为企业管理员',
    user: 'admin',
    ip_address: '*************',
    timestamp: new Date('2025-01-15T09:15:00Z'),
    handled: false
  },
  {
    id: 3,
    type: 'security',
    severity: 'error',
    title: '异常登录尝试',
    description: '检测到来自 ************* 的多次失败登录尝试',
    user: 'unknown',
    ip_address: '*************',
    timestamp: new Date('2025-01-15T08:45:00Z'),
    handled: false
  }
])

// 审计日志相关
const auditDateRange = ref([])
const auditLogLevel = ref('all')
const auditLogs = ref([
  {
    id: 1,
    timestamp: new Date('2025-01-15T10:30:00Z'),
    level: 'info',
    user: 'admin',
    action: '用户登录',
    resource: '系统',
    ip_address: '*************',
    details: '用户成功登录系统'
  },
  {
    id: 2,
    timestamp: new Date('2025-01-15T10:25:00Z'),
    level: 'info',
    user: 'admin',
    action: '创建用户',
    resource: 'user:jane.smith',
    ip_address: '*************',
    details: '创建新用户 jane.smith'
  },
  {
    id: 3,
    timestamp: new Date('2025-01-15T10:20:00Z'),
    level: 'warning',
    user: 'john.doe',
    action: '权限变更',
    resource: 'role:enterprise_admin',
    ip_address: '*************',
    details: '角色权限被修改'
  }
])

// 权限配置相关
const checkedPermissions = ref([])
const permissionTreeData = ref([
  {
    title: '用户管理',
    key: 'user',
    description: '用户相关操作权限',
    children: [
      { title: '创建用户', key: 'user.create', description: '创建新用户账户' },
      { title: '查看用户', key: 'user.read', description: '查看用户信息' },
      { title: '编辑用户', key: 'user.update', description: '修改用户信息' },
      { title: '删除用户', key: 'user.delete', description: '删除用户账户' }
    ]
  },
  {
    title: '角色管理',
    key: 'role',
    description: '角色相关操作权限',
    children: [
      { title: '创建角色', key: 'role.create', description: '创建新角色' },
      { title: '查看角色', key: 'role.read', description: '查看角色信息' },
      { title: '编辑角色', key: 'role.update', description: '修改角色权限' },
      { title: '删除角色', key: 'role.delete', description: '删除角色' }
    ]
  },
  {
    title: '资源管理',
    key: 'resource',
    description: '资源相关操作权限',
    children: [
      { title: '创建资源', key: 'resource.create', description: '创建新资源' },
      { title: '查看资源', key: 'resource.read', description: '查看资源信息' },
      { title: '编辑资源', key: 'resource.update', description: '修改资源配置' },
      { title: '删除资源', key: 'resource.delete', description: '删除资源' }
    ]
  }
])

// 用户数据
const users = ref([
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'system_admin',
    is_active: true,
    ldap_dn: 'cn=admin,ou=admins,dc=company,dc=com',
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login_at: new Date('2025-01-15T10:30:00Z')
  },
  {
    id: '2',
    username: 'john.doe',
    email: '<EMAIL>',
    role: 'enterprise_admin',
    is_active: true,
    ldap_dn: 'cn=john.doe,ou=users,dc=company,dc=com',
    created_at: new Date('2025-01-05T00:00:00Z'),
    last_login_at: new Date('2025-01-15T09:15:00Z')
  },
  {
    id: '3',
    username: 'jane.smith',
    email: '<EMAIL>',
    role: 'personal',
    is_active: true,
    ldap_dn: null,
    created_at: new Date('2025-01-10T00:00:00Z'),
    last_login_at: new Date('2025-01-14T16:45:00Z')
  }
])

// 角色数据
const roles = ref([
  {
    id: 'system_admin',
    name: '系统管理员',
    description: '拥有系统的完全访问权限，可以管理所有功能和用户',
    permissions: [
      'user.create',
      'user.read',
      'user.update',
      'user.delete',
      'role.create',
      'role.read',
      'role.update',
      'role.delete',
      'resource.create',
      'resource.read',
      'resource.update',
      'resource.delete',
      'sync.config',
      'sync.monitor',
      'sync.execute',
      'security.audit',
      'security.config',
      'system.config',
      'system.monitor'
    ]
  },
  {
    id: 'enterprise_admin',
    name: '企业管理员',
    description: '管理企业资源和用户，配置同步策略',
    permissions: [
      'user.read',
      'user.update',
      'resource.create',
      'resource.read',
      'resource.update',
      'resource.delete',
      'sync.config',
      'sync.monitor',
      'sync.execute',
      'security.audit'
    ]
  },
  {
    id: 'personal',
    name: '个人用户',
    description: '管理个人资源，查看企业共享资源',
    permissions: ['resource.read', 'resource.personal.create', 'resource.personal.update', 'resource.personal.delete']
  }
])

// 权限定义
const permissions = ref([
  { key: 'user.create', label: '创建用户', category: 'user' },
  { key: 'user.read', label: '查看用户', category: 'user' },
  { key: 'user.update', label: '编辑用户', category: 'user' },
  { key: 'user.delete', label: '删除用户', category: 'user' },
  { key: 'role.create', label: '创建角色', category: 'role' },
  { key: 'role.read', label: '查看角色', category: 'role' },
  { key: 'role.update', label: '编辑角色', category: 'role' },
  { key: 'role.delete', label: '删除角色', category: 'role' },
  { key: 'resource.create', label: '创建资源', category: 'resource' },
  { key: 'resource.read', label: '查看资源', category: 'resource' },
  { key: 'resource.update', label: '编辑资源', category: 'resource' },
  { key: 'resource.delete', label: '删除资源', category: 'resource' },
  { key: 'resource.personal.create', label: '创建个人资源', category: 'resource' },
  { key: 'resource.personal.update', label: '编辑个人资源', category: 'resource' },
  { key: 'resource.personal.delete', label: '删除个人资源', category: 'resource' },
  { key: 'sync.config', label: '配置同步', category: 'sync' },
  { key: 'sync.monitor', label: '监控同步', category: 'sync' },
  { key: 'sync.execute', label: '执行同步', category: 'sync' },
  { key: 'security.audit', label: '安全审计', category: 'security' },
  { key: 'security.config', label: '安全配置', category: 'security' },
  { key: 'system.config', label: '系统配置', category: 'system' },
  { key: 'system.monitor', label: '系统监控', category: 'system' }
])

// 权限分类
const permissionCategories = computed(() => [
  {
    key: 'user',
    title: '用户管理',
    permissions: permissions.value.filter((p) => p.category === 'user')
  },
  {
    key: 'role',
    title: '角色管理',
    permissions: permissions.value.filter((p) => p.category === 'role')
  },
  {
    key: 'resource',
    title: '资源管理',
    permissions: permissions.value.filter((p) => p.category === 'resource')
  },
  {
    key: 'sync',
    title: '同步管理',
    permissions: permissions.value.filter((p) => p.category === 'sync')
  },
  {
    key: 'security',
    title: '安全管理',
    permissions: permissions.value.filter((p) => p.category === 'security')
  },
  {
    key: 'system',
    title: '系统管理',
    permissions: permissions.value.filter((p) => p.category === 'system')
  }
])

// 表单数据
const userFormData = reactive({
  username: '',
  email: '',
  role: '',
  is_active: true,
  ldap_dn: '',
  password: ''
})

const roleFormData = reactive({
  name: '',
  description: ''
})

// 表单验证规则
const userFormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const roleFormRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入角色描述', trigger: 'blur' }]
}

// 计算属性
const activeUsers = computed(() => {
  return users.value.filter((user) => user.is_active).length
})

const filteredUsers = computed(() => {
  if (!userSearchKeyword.value) return users.value
  const keyword = userSearchKeyword.value.toLowerCase()
  return users.value.filter((user) => user.username.toLowerCase().includes(keyword) || user.email.toLowerCase().includes(keyword))
})

const selectedRole = computed(() => {
  return roles.value.find((role) => role.id === selectedRoleId.value)
})

// 安全事件相关计算属性
const filteredSecurityEvents = computed(() => {
  if (eventFilter.value === 'all') return securityEvents.value
  return securityEvents.value.filter((event) => event.type === eventFilter.value)
})

// 审计日志相关计算属性
const filteredAuditLogs = computed(() => {
  let filtered = auditLogs.value

  if (auditLogLevel.value !== 'all') {
    filtered = filtered.filter((log) => log.level === auditLogLevel.value)
  }

  if (auditDateRange.value && auditDateRange.value.length === 2) {
    const [startDate, endDate] = auditDateRange.value
    filtered = filtered.filter((log) => {
      const logDate = new Date(log.timestamp)
      return logDate >= startDate && logDate <= endDate
    })
  }

  return filtered
})

// 审计日志表格列定义
const auditColumns = [
  {
    title: '时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 180,
    customRender: ({ text }) => formatTime(text)
  },
  {
    title: '级别',
    dataIndex: 'level',
    key: 'level',
    width: 80
  },
  {
    title: '用户',
    dataIndex: 'user',
    key: 'user',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120
  },
  {
    title: '资源',
    dataIndex: 'resource',
    key: 'resource',
    width: 150
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    key: 'ip_address',
    width: 120
  },
  {
    title: '详情',
    dataIndex: 'details',
    key: 'details'
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
]

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const formatTime = (date: Date) => {
  if (!date) return '从未'
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const getRoleColor = (role: string) => {
  const colors = {
    system_admin: 'red',
    enterprise_admin: 'orange',
    personal: 'blue'
  }
  return colors[role] || 'default'
}

const getRoleLabel = (role: string) => {
  const labels = {
    system_admin: '系统管理员',
    enterprise_admin: '企业管理员',
    personal: '个人用户'
  }
  return labels[role] || role
}

const getPermissionLabel = (permission: string) => {
  const perm = permissions.value.find((p) => p.key === permission)
  return perm ? perm.label : permission
}

const getUserPermissions = (user: any) => {
  const role = roles.value.find((r) => r.id === user.role)
  return role ? role.permissions : []
}

const hasPermission = (permission: string) => {
  return selectedRole.value?.permissions.includes(permission) || false
}

const isCategoryFullySelected = (category: string) => {
  const categoryPermissions = permissionCategories.value.find((c) => c.key === category)?.permissions || []
  return categoryPermissions.every((p) => hasPermission(p.key))
}

const isCategoryPartiallySelected = (category: string) => {
  const categoryPermissions = permissionCategories.value.find((c) => c.key === category)?.permissions || []
  const selectedCount = categoryPermissions.filter((p) => hasPermission(p.key)).length
  return selectedCount > 0 && selectedCount < categoryPermissions.length
}

const handleUserSearch = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredUsers 计算属性中处理
  console.log('搜索用户:', userSearchKeyword.value)
}

const selectUser = (user: any) => {
  selectedUser.value = user
}

const closeUserDetails = () => {
  selectedUser.value = null
}

const handleRoleChange = () => {
  // 角色变更逻辑已在计算属性中处理
}

const togglePermission = (permission: string) => {
  if (!selectedRole.value) return

  const index = selectedRole.value.permissions.indexOf(permission)
  if (index > -1) {
    selectedRole.value.permissions.splice(index, 1)
  } else {
    selectedRole.value.permissions.push(permission)
  }
}

const toggleCategoryPermissions = (category: string) => {
  const categoryPermissions = permissionCategories.value.find((c) => c.key === category)?.permissions || []
  const isFullySelected = isCategoryFullySelected(category)

  if (isFullySelected) {
    // 取消选择所有权限
    categoryPermissions.forEach((p) => {
      const index = selectedRole.value.permissions.indexOf(p.key)
      if (index > -1) {
        selectedRole.value.permissions.splice(index, 1)
      }
    })
  } else {
    // 选择所有权限
    categoryPermissions.forEach((p) => {
      if (!selectedRole.value.permissions.includes(p.key)) {
        selectedRole.value.permissions.push(p.key)
      }
    })
  }
}

const saveRolePermissions = () => {
  message.success('角色权限保存成功')
}

const resetRolePermissions = () => {
  // 重置权限到原始状态
  message.info('权限已重置')
}

// 安全事件相关方法
const refreshSecurityEvents = () => {
  // 模拟刷新安全事件
  message.success('安全事件已刷新')
}

const getEventIcon = (eventType: string) => {
  const iconMap = {
    login: UserCheck,
    permission: Key,
    security: Shield
  }
  return iconMap[eventType] || Shield
}

const viewEventDetails = (event: any) => {
  notification.info({
    message: '事件详情',
    description: `${event.title}: ${event.description}`,
    duration: 5
  })
}

const markEventAsHandled = (eventId: number) => {
  const event = securityEvents.value.find((e) => e.id === eventId)
  if (event) {
    event.handled = true
    message.success('事件已标记为已处理')
  }
}

// 审计日志相关方法
const handleDateRangeChange = (dates: any) => {
  auditDateRange.value = dates
}

const exportAuditLogs = () => {
  const data = filteredAuditLogs.value.map((log) => ({
    时间: formatTime(log.timestamp),
    级别: log.level.toUpperCase(),
    用户: log.user,
    操作: log.action,
    资源: log.resource,
    IP地址: log.ip_address,
    详情: log.details
  }))

  const dataStr = JSON.stringify(data, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `audit_logs_${new Date().toISOString().split('T')[0]}.json`
  link.click()

  message.success('审计日志导出成功')
}

const viewLogDetails = (record: any) => {
  notification.info({
    message: '日志详情',
    description: `操作: ${record.action}\n资源: ${record.resource}\n详情: ${record.details}`,
    duration: 5
  })
}

const getLogLevelColor = (level: string) => {
  const colorMap = {
    info: 'blue',
    warning: 'orange',
    error: 'red'
  }
  return colorMap[level] || 'default'
}

// 权限配置相关方法
const addPermission = () => {
  // 添加新权限的逻辑
  message.info('添加权限功能开发中')
}

const handlePermissionCheck = (checkedKeys: any) => {
  checkedPermissions.value = checkedKeys
}

const showUserModal = () => {
  editingUser.value = null
  resetUserForm()
  showUserModalVisible.value = true
}

const showRoleModal = () => {
  editingRole.value = null
  resetRoleForm()
  showRoleModalVisible.value = true
}

const editUser = (user: any) => {
  editingUser.value = user
  Object.assign(userFormData, user)
  showUserModalVisible.value = true
}

const resetPassword = (user: any) => {
  message.success(`${user.username} 的密码重置成功`)
}

const toggleUserStatus = (user: any) => {
  user.is_active = !user.is_active
  message.success(`用户已${user.is_active ? '启用' : '禁用'}`)
}

const deleteUser = (user: any) => {
  const index = users.value.findIndex((u) => u.id === user.id)
  if (index > -1) {
    users.value.splice(index, 1)
    if (selectedUser.value?.id === user.id) {
      selectedUser.value = null
    }
    message.success('用户删除成功')
  }
}

const resetUserForm = () => {
  Object.assign(userFormData, {
    username: '',
    email: '',
    role: '',
    is_active: true,
    ldap_dn: '',
    password: ''
  })
}

const resetRoleForm = () => {
  Object.assign(roleFormData, {
    name: '',
    description: ''
  })
}

const handleSaveUser = () => {
  if (editingUser.value) {
    // 更新现有用户
    Object.assign(editingUser.value, userFormData)
    message.success('用户信息更新成功')
  } else {
    // 添加新用户
    const newUser = {
      ...userFormData,
      id: Date.now().toString(),
      created_at: new Date(),
      last_login_at: null
    }
    users.value.push(newUser)
    message.success('用户创建成功')
  }
  showUserModalVisible.value = false
}

const handleCancelUser = () => {
  showUserModalVisible.value = false
  resetUserForm()
}

const handleSaveRole = () => {
  if (editingRole.value) {
    // 更新现有角色
    Object.assign(editingRole.value, roleFormData)
    message.success('角色信息更新成功')
  } else {
    // 添加新角色
    const newRole = {
      ...roleFormData,
      id: Date.now().toString(),
      permissions: []
    }
    roles.value.push(newRole)
    message.success('角色创建成功')
  }
  showRoleModalVisible.value = false
}

const handleCancelRole = () => {
  showRoleModalVisible.value = false
  resetRoleForm()
}

// 生命周期
onMounted(() => {
  // 默认选择第一个角色
  if (roles.value.length > 0) {
    selectedRoleId.value = roles.value[0].id
  }
})
</script>

<style scoped>
.permission-management {
  padding: 20px;
  background: #f8fafc !important;
  min-height: 100vh;
  overflow-y: auto !important;
  overflow-x: hidden;
  /* 禁用窗口拖拽以确保滚动正常 */
  -webkit-app-region: no-drag !important;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white !important;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e5e7eb;
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #6b7280;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  white-space: nowrap;
}

.back-button:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.back-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.permission-overview {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.main-content {
  margin-bottom: 24px;
}

.users-card,
.roles-card {
  height: auto;
  max-height: 600px;
}

.users-list {
  max-height: 520px;
  overflow-y: auto !important;
  padding-right: 8px;
}

/* 修复搜索框样式 */
.ant-input-search {
  background: white !important;
}

.ant-input-search .ant-input {
  background: white !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

.ant-input-search .ant-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-input-search .ant-input::placeholder {
  color: #9ca3af !important;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
}

.user-item:hover {
  background: #f5f5f5;
}

.user-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.user-avatar {
  margin-right: 12px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.user-role {
  margin-bottom: 0;
}

.user-status {
  margin-right: 12px;
}

.user-actions {
  margin-left: 8px;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.menu-icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.danger-item {
  color: #ef4444;
}

.role-permissions {
  max-height: 520px;
  overflow-y: auto;
}

.role-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.role-name {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.role-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.permissions-grid {
  margin-bottom: 24px;
}

.permission-category {
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.permission-items {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.role-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.no-role-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #6b7280;
}

.no-role-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.user-details {
  margin-bottom: 24px;
}

.details-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-profile {
  padding: 8px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.profile-info {
  margin-left: 16px;
}

.profile-name {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.profile-email {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
}

.profile-details {
  margin-bottom: 24px;
}

.user-permissions h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 统一按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f8fafc;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f1f5f9;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 返回按钮 */
.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-back:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-back:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}

.btn-back .btn-text {
  white-space: nowrap;
}

/* 安全事件样式 */
.security-events {
  padding: 16px;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: var(--text-color);
}

.header-left p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.events-list {
  max-height: 500px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 8px;
}

.event-item.info {
  border-left: 4px solid #1890ff;
}

.event-item.warning {
  border-left: 4px solid #faad14;
}

.event-item.error {
  border-left: 4px solid #ff4d4f;
}

.event-icon {
  margin-top: 2px;

  svg {
    width: 16px;
    height: 16px;
    color: var(--primary-color);
  }
}

.event-content {
  flex: 1;
}

.event-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.event-description {
  color: var(--text-color-secondary);
  font-size: 14px;
  margin-bottom: 8px;
}

.event-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--text-color-tertiary);
}

.event-actions {
  display: flex;
  gap: 8px;
}

/* 审计日志样式 */
.audit-logs {
  padding: 16px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

/* 权限配置样式 */
.permission-config {
  padding: 16px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.permission-tree {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 16px;
}

.permission-node {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.permission-title {
  font-weight: 500;
  color: var(--text-color);
}

.permission-desc {
  font-size: 12px;
  color: var(--text-color-secondary);
}

/* 标签页样式增强 */
.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.ant-tabs-content-holder {
  background: var(--bg-color);
  border-radius: 0 0 8px 8px;
}

/* 搜索框样式修复 - 彻底覆盖Ant Design样式 */
.custom-search-input {
  -webkit-app-region: no-drag;
}

/* 最高优先级的样式覆盖 */
.custom-search-input.ant-input-search,
.custom-search-input .ant-input-search,
.custom-search-input .ant-input-search .ant-input-group,
.custom-search-input .ant-input-search .ant-input-group .ant-input-affix-wrapper,
.custom-search-input .ant-input-search .ant-input-affix-wrapper,
.custom-search-input .ant-input-affix-wrapper,
.custom-search-input .ant-input {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  color: #262626 !important;
}

/* 暗色主题下的样式 */
.theme-dark .custom-search-input.ant-input-search,
.theme-dark .custom-search-input .ant-input-search,
.theme-dark .custom-search-input .ant-input-search .ant-input-group,
.theme-dark .custom-search-input .ant-input-search .ant-input-group .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input-search .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input {
  background-color: #1f1f1f !important;
  background: #1f1f1f !important;
  border: 1px solid #4b5563 !important;
  color: #e2e8f0 !important;
}

/* 输入框内部元素 */
.custom-search-input input,
.custom-search-input .ant-input-affix-wrapper input,
.custom-search-input .ant-input-search input {
  background-color: transparent !important;
  background: transparent !important;
  color: inherit !important;
  padding-left: 11px !important;
}

/* 占位符样式 */
.custom-search-input .ant-input::placeholder,
.custom-search-input input::placeholder {
  color: #bfbfbf !important;
}

.theme-dark .custom-search-input .ant-input::placeholder,
.theme-dark .custom-search-input input::placeholder {
  color: #666666 !important;
}

/* 搜索按钮样式 */
.custom-search-input .ant-input-search-button {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.custom-search-input .ant-input-search-button:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 焦点状态 */
.custom-search-input .ant-input-affix-wrapper:focus,
.custom-search-input .ant-input-affix-wrapper-focused,
.custom-search-input .ant-input:focus {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 悬停状态 */
.custom-search-input .ant-input-affix-wrapper:hover,
.custom-search-input .ant-input:hover {
  border-color: #40a9ff !important;
}
</style>
