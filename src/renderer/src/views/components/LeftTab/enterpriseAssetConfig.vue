<template>
  <div class="enterprise-asset-config">
    <div class="header">
      <h2>企业资产管理</h2>
      <p>管理企业主机的主机配置、密钥链和代码片段，以及同步、监控、安全功能</p>
    </div>

    <div class="search-section">
      <div class="search-wrapper">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索企业资产..."
          class="search-input"
          @input="handleSearch"
        >
          <template #suffix>
            <SearchOutlined />
          </template>
        </a-input>
        <div class="action-buttons">
          <a-button
            size="small"
            type="primary"
            class="action-button"
            @click="handleNewAsset"
          >
            <template #icon><PlusOutlined /></template>
            添加主机
          </a-button>
          <a-button
            size="small"
            class="action-button"
            @click="handleImportAssets"
          >
            <template #icon><ImportOutlined /></template>
            导入
          </a-button>
          <a-button
            size="small"
            class="action-button"
            @click="handleExportAssets"
          >
            <template #icon><ExportOutlined /></template>
            导出
          </a-button>
        </div>
      </div>
    </div>

    <!-- 过滤器区域 -->
    <div class="filter-section">
      <div class="filter-wrapper">
        <div class="filter-item">
          <span class="filter-label">设备类型:</span>
          <a-select
            v-model:value="filters.type"
            placeholder="全部类型"
            style="width: 120px"
            size="small"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="database">数据库</a-select-option>
            <a-select-option value="network">网络设备</a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <span class="filter-label">部门:</span>
          <a-select
            v-model:value="filters.department"
            placeholder="全部部门"
            style="width: 120px"
            size="small"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部部门</a-select-option>
            <a-select-option value="技术部">技术部</a-select-option>
            <a-select-option value="数据部">数据部</a-select-option>
            <a-select-option value="网络部">网络部</a-select-option>
            <a-select-option value="运维部">运维部</a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <span class="filter-label">业务类型:</span>
          <a-select
            v-model:value="filters.businessType"
            placeholder="全部业务"
            style="width: 120px"
            size="small"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部业务</a-select-option>
            <a-select-option value="生产">生产</a-select-option>
            <a-select-option value="测试">测试</a-select-option>
            <a-select-option value="开发">开发</a-select-option>
            <a-select-option value="备份">备份</a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <span class="filter-label">地区:</span>
          <a-select
            v-model:value="filters.region"
            placeholder="全部地区"
            style="width: 120px"
            size="small"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部地区</a-select-option>
            <a-select-option value="北京">北京</a-select-option>
            <a-select-option value="上海">上海</a-select-option>
            <a-select-option value="深圳">深圳</a-select-option>
            <a-select-option value="广州">广州</a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <span class="filter-label">状态:</span>
          <a-select
            v-model:value="filters.status"
            placeholder="全部状态"
            style="width: 100px"
            size="small"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="unknown">未知</a-select-option>
          </a-select>
        </div>

        <a-button
          size="small"
          @click="resetFilters"
        >
          重置
        </a-button>
      </div>
    </div>

    <div class="assets-section">
      <!-- 分组显示 -->
      <div
        v-if="shouldShowGrouped"
        class="grouped-assets"
      >
        <div
          v-for="group in groupedAssets"
          :key="group.type"
          class="asset-group"
        >
          <div class="group-header">
            <h3 class="group-title">
              <DesktopOutlined v-if="group.type === 'server'" />
              <DatabaseOutlined v-if="group.type === 'database'" />
              <CloudServerOutlined v-if="group.type === 'network'" />
              {{ getAssetTypeLabel(group.type) }}
              <span class="group-count">({{ group.assets.length }})</span>
            </h3>
          </div>
          <div class="assets-grid">
            <div
              v-for="asset in group.assets"
              :key="asset.id"
              class="asset-card"
              @click="handleAssetClick(asset)"
              @dblclick="handleAssetConnect(asset)"
              @contextmenu.prevent="handleAssetContextMenu($event, asset)"
            >
              <div class="asset-header">
                <div class="asset-icon">
                  <DesktopOutlined v-if="asset.type === 'server'" />
                  <DatabaseOutlined v-if="asset.type === 'database'" />
                  <CloudServerOutlined v-if="asset.type === 'network'" />
                  <CodeOutlined v-if="asset.type === 'console'" />
                  <WindowsOutlined v-if="asset.type === 'rdp'" />
                </div>
                <div
                  class="asset-edit"
                  @click.stop="handleAssetEdit(asset)"
                >
                  <EditOutlined />
                </div>
              </div>
              <div class="asset-content">
                <h4 class="asset-name">{{ asset.name }}</h4>
                <p class="asset-address">{{ asset.host }}:{{ asset.port }}</p>
                <div class="asset-meta">
                  <span class="asset-type">{{ getAssetTypeLabel(asset.type) }}</span>
                  <span
                    class="asset-status"
                    :class="asset.status"
                  >
                    {{ getStatusLabel(asset.status) }}
                  </span>
                </div>
                <div class="asset-tags">
                  <span
                    v-if="asset.department"
                    class="asset-tag"
                    >{{ asset.department }}</span
                  >
                  <span
                    v-if="asset.region"
                    class="asset-tag"
                    >{{ asset.region }}</span
                  >
                  <span
                    v-if="asset.businessType"
                    class="asset-tag"
                    >{{ asset.businessType }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 普通列表显示 -->
      <div
        v-else
        class="assets-grid"
      >
        <div
          v-for="asset in filteredAssets"
          :key="asset.id"
          class="asset-card"
          @click="handleAssetClick(asset)"
          @dblclick="handleAssetConnect(asset)"
          @contextmenu.prevent="handleAssetContextMenu($event, asset)"
        >
          <div class="asset-header">
            <div class="asset-icon">
              <DesktopOutlined v-if="asset.type === 'server'" />
              <DatabaseOutlined v-if="asset.type === 'database'" />
              <CloudServerOutlined v-if="asset.type === 'network'" />
              <CodeOutlined v-if="asset.type === 'console'" />
              <WindowsOutlined v-if="asset.type === 'rdp'" />
            </div>
            <div
              class="asset-edit"
              @click.stop="handleAssetEdit(asset)"
            >
              <EditOutlined />
            </div>
          </div>
          <div class="asset-content">
            <h4 class="asset-name">{{ asset.name }}</h4>
            <p class="asset-address">{{ asset.host }}:{{ asset.port }}</p>
            <div class="asset-meta">
              <span class="asset-type">{{ getAssetTypeLabel(asset.type) }}</span>
              <span
                class="asset-status"
                :class="asset.status"
              >
                {{ getStatusLabel(asset.status) }}
              </span>
            </div>
            <div class="asset-tags">
              <span
                v-if="asset.department"
                class="asset-tag"
                >{{ asset.department }}</span
              >
              <span
                v-if="asset.region"
                class="asset-tag"
                >{{ asset.region }}</span
              >
              <span
                v-if="asset.businessType"
                class="asset-tag"
                >{{ asset.businessType }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑/新建表单模态框 -->
    <a-modal
      v-model:open="isModalVisible"
      :title="isEditMode ? '编辑企业资产' : '添加企业资产'"
      width="600px"
      @ok="handleFormSubmit"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="资产名称"
              name="name"
            >
              <a-input
                v-model:value="formData.name"
                placeholder="请输入资产名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="资产类型"
              name="type"
            >
              <a-select
                v-model:value="formData.type"
                placeholder="请选择资产类型"
              >
                <a-select-option value="server">服务器</a-select-option>
                <a-select-option value="database">数据库</a-select-option>
                <a-select-option value="network">网络设备</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="16">
            <a-form-item
              label="主机地址"
              name="host"
            >
              <a-input
                v-model:value="formData.host"
                placeholder="请输入IP地址或域名"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="端口"
              name="port"
            >
              <a-input-number
                v-model:value="formData.port"
                :min="1"
                :max="65535"
                placeholder="端口号"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="用户名"
              name="username"
            >
              <a-input
                v-model:value="formData.username"
                placeholder="请输入用户名"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="密码"
              name="password"
            >
              <a-input-password
                v-model:value="formData.password"
                placeholder="请输入密码"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="所属组织"
              name="organization"
            >
              <a-input
                v-model:value="formData.organization"
                placeholder="请输入组织名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="状态"
              name="status"
            >
              <a-select
                v-model:value="formData.status"
                placeholder="请选择状态"
              >
                <a-select-option value="online">在线</a-select-option>
                <a-select-option value="offline">离线</a-select-option>
                <a-select-option value="unknown">未知</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入资产描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 右键菜单 -->
    <EnterpriseAssetContextMenu
      :visible="contextMenuVisible"
      :position="contextMenuPosition"
      :asset="selectedAsset"
      @close="closeContextMenu"
      @connect="handleContextMenuConnect"
      @edit="handleContextMenuEdit"
      @create-folder="handleCreateFolderFromContextMenu"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import {
  DesktopOutlined,
  CloudServerOutlined,
  DatabaseOutlined,
  SearchOutlined,
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  EditOutlined,
  CodeOutlined,
  WindowsOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import eventBus from '@/utils/eventBus'
import EnterpriseAssetContextMenu from './components/EnterpriseAssetContextMenu.vue'

interface EnterpriseAsset {
  id: string
  name: string
  host: string
  port: number
  type: 'server' | 'database' | 'network' | 'console' | 'rdp'
  status: 'online' | 'offline' | 'unknown'
  organization?: string
  department?: string
  businessType?: string
  region?: string
  environment?: string
  username?: string
  password?: string
  description?: string
  // Console和RDP连接类型支持
  connection_type?: 'ssh' | 'console' | 'rdp'
  console_type?: 'serial' | 'telnet' | 'ssh'
  serial_config?: {
    baudRate?: number
    dataBits?: number
    stopBits?: number
    parity?: 'none' | 'even' | 'odd'
    device?: string
  }
  telnet_config?: {
    timeout?: number
  }
  rdp_config?: {
    domain?: string
    display_settings?: {
      width?: number
      height?: number
      colorDepth?: number
    }
    security_settings?: {
      encryption?: boolean
      authentication?: 'ntlm' | 'basic'
    }
  }
}

// 响应式数据
const searchKeyword = ref('')
const isModalVisible = ref(false)
const isEditMode = ref(false)
const editingAssetId = ref<string | null>(null)
const formRef = ref()

// 过滤器数据
const filters = ref({
  type: '',
  department: '',
  businessType: '',
  region: '',
  status: ''
})

// 右键菜单数据
const contextMenuVisible = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const selectedAsset = ref<EnterpriseAsset | null>(null)

// 表单数据
const formData = ref<EnterpriseAsset>({
  id: '',
  name: '',
  host: '',
  port: 22,
  type: 'server',
  status: 'unknown',
  organization: '',
  department: '',
  businessType: '',
  region: '',
  environment: '',
  username: '',
  password: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择资产类型', trigger: 'change' }]
}
const assets = ref<EnterpriseAsset[]>([
  {
    id: '1',
    name: '生产服务器-01',
    host: '***********00',
    port: 22,
    type: 'server',
    status: 'online',
    organization: '技术部',
    department: '技术部',
    businessType: '生产',
    region: '北京',
    environment: '生产环境',
    username: 'admin',
    password: 'password123',
    description: '主要生产环境服务器'
  },
  {
    id: '2',
    name: '数据库服务器',
    host: '*************',
    port: 3306,
    type: 'database',
    status: 'online',
    organization: '数据部',
    department: '数据部',
    businessType: '生产',
    region: '上海',
    environment: '生产环境',
    username: 'dbadmin',
    password: 'dbpass123',
    description: 'MySQL主数据库服务器'
  },
  {
    id: '3',
    name: '网络设备-交换机',
    host: '***********',
    port: 23,
    type: 'network',
    status: 'offline',
    organization: '网络部',
    department: '网络部',
    businessType: '基础设施',
    region: '深圳',
    environment: '生产环境',
    username: 'netadmin',
    password: 'netpass123',
    description: '核心网络交换设备'
  },
  {
    id: '4',
    name: '测试服务器-01',
    host: '*************',
    port: 22,
    type: 'server',
    status: 'online',
    organization: '技术部',
    department: '技术部',
    businessType: '测试',
    region: '北京',
    environment: '测试环境',
    username: 'testuser',
    password: 'testpass123',
    description: '测试环境服务器'
  },
  {
    id: '5',
    name: '开发服务器-01',
    host: '*************',
    port: 22,
    type: 'server',
    status: 'unknown',
    organization: '技术部',
    department: '技术部',
    businessType: '开发',
    region: '广州',
    environment: '开发环境',
    username: 'devuser',
    password: 'devpass123',
    description: '开发环境服务器'
  },
  {
    id: '6',
    name: '备份数据库',
    host: '***********02',
    port: 3306,
    type: 'database',
    status: 'online',
    organization: '数据部',
    department: '运维部',
    businessType: '备份',
    region: '上海',
    environment: '生产环境',
    username: 'backupuser',
    password: 'backuppass123',
    description: '数据备份服务器'
  }
])

// 计算属性
const filteredAssets = computed(() => {
  let filtered = assets.value

  // 应用搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(
      (asset) =>
        asset.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        asset.host.includes(searchKeyword.value) ||
        asset.organization?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        asset.department?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        asset.region?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 应用过滤器
  if (filters.value.type) {
    filtered = filtered.filter((asset) => asset.type === filters.value.type)
  }
  if (filters.value.department) {
    filtered = filtered.filter((asset) => asset.department === filters.value.department)
  }
  if (filters.value.businessType) {
    filtered = filtered.filter((asset) => asset.businessType === filters.value.businessType)
  }
  if (filters.value.region) {
    filtered = filtered.filter((asset) => asset.region === filters.value.region)
  }
  if (filters.value.status) {
    filtered = filtered.filter((asset) => asset.status === filters.value.status)
  }

  return filtered
})

// 判断是否应该显示分组
const shouldShowGrouped = computed(() => {
  // 当没有应用任何过滤器时，显示分组
  return !filters.value.type && !filters.value.department && !filters.value.businessType && !filters.value.region && !filters.value.status
})

// 分组资产
const groupedAssets = computed(() => {
  const groups = new Map()

  filteredAssets.value.forEach((asset) => {
    if (!groups.has(asset.type)) {
      groups.set(asset.type, {
        type: asset.type,
        assets: []
      })
    }
    groups.get(asset.type).assets.push(asset)
  })

  // 按类型排序：服务器 -> 数据库 -> 网络设备
  const sortOrder = ['server', 'database', 'network']
  return Array.from(groups.values()).sort((a, b) => {
    return sortOrder.indexOf(a.type) - sortOrder.indexOf(b.type)
  })
})

// 方法
const handleSearch = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredAssets 计算属性中处理
  console.log('搜索企业资产:', searchKeyword.value)
}

// 过滤器方法
const handleFilterChange = () => {
  console.log('过滤器变化:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    type: '',
    department: '',
    businessType: '',
    region: '',
    status: ''
  }
  console.log('重置过滤器')
}

// 右键菜单方法
const handleAssetContextMenu = (event: MouseEvent, asset: EnterpriseAsset) => {
  event.preventDefault()
  selectedAsset.value = asset
  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
  contextMenuVisible.value = true
  console.log('显示右键菜单:', asset)
}

const closeContextMenu = () => {
  contextMenuVisible.value = false
  selectedAsset.value = null
}

const handleContextMenuConnect = () => {
  if (selectedAsset.value) {
    handleAssetConnect(selectedAsset.value)
  }
  closeContextMenu()
}

const handleContextMenuEdit = () => {
  if (selectedAsset.value) {
    handleAssetEdit(selectedAsset.value)
  }
  closeContextMenu()
}

const handleCreateFolderFromContextMenu = async () => {
  try {
    // 弹出输入框让用户输入收藏夹名称
    const folderName = prompt('请输入收藏夹名称:')

    if (!folderName || !folderName.trim()) {
      message.warning('收藏夹名称不能为空')
      closeContextMenu()
      return
    }

    // 创建新收藏夹
    const api = window.api as any
    const createResult = await api.createCustomFolder({
      name: folderName.trim(),
      description: ''
    })

    if (createResult && createResult.data && createResult.data.message === 'success') {
      message.success(`收藏夹 "${folderName.trim()}" 创建成功`)

      // 如果有选中的资产，自动将其添加到新创建的收藏夹
      if (selectedAsset.value && createResult.data.folder) {
        const folderUuid = createResult.data.folder.uuid

        // 收藏当前选中的资产到新创建的收藏夹
        const favoriteResult = await api.moveEnterpriseAssetToFolder({
          folderUuid: folderUuid,
          assetData: {
            name: selectedAsset.value.name,
            host: selectedAsset.value.host,
            port: selectedAsset.value.port,
            type: selectedAsset.value.type,
            organization: selectedAsset.value.organization,
            department: selectedAsset.value.department,
            businessType: selectedAsset.value.businessType,
            region: selectedAsset.value.region,
            description: selectedAsset.value.description
          }
        })

        if (favoriteResult && favoriteResult.data && favoriteResult.data.message === 'success') {
          message.success(`已将 "${selectedAsset.value.name}" 收藏到 "${folderName.trim()}"`)
        }
      }

      // 刷新左侧栏
      eventBus.emit('LocalAssetMenu')
    } else {
      const errorMsg = createResult?.data?.error || '创建失败'
      message.error(`创建收藏夹失败: ${errorMsg}`)
    }
  } catch (error) {
    console.error('创建收藏夹过程中出错:', error)
    message.error(`创建收藏夹失败: ${error.message || '网络错误'}`)
  }

  closeContextMenu()
}

const handleAssetClick = (asset: EnterpriseAsset) => {
  console.log('点击企业资产:', asset)
}

const handleAssetConnect = (asset: EnterpriseAsset) => {
  console.log('连接企业资产:', asset)

  // 根据资产类型处理不同的连接逻辑
  switch (asset.type) {
    case 'console':
      handleConsoleConnect(asset)
      break
    case 'rdp':
      handleRdpConnect(asset)
      break
    case 'server':
    case 'database':
    case 'network':
    default:
      handleSshConnect(asset)
      break
  }
}

// SSH连接处理（原有逻辑）
const handleSshConnect = (asset: EnterpriseAsset) => {
  // 将企业资产数据转换为个人资产格式，以便复用现有的连接逻辑
  const assetNode = {
    uuid: asset.id,
    label: asset.name,
    ip: asset.host,
    port: asset.port,
    username: asset.username || '',
    password: asset.password || '',
    group_name: asset.organization || '企业资产',
    asset_type: 'organization', // 标记为企业资产
    connection_type: 'ssh',
    // 其他必要字段
    key_chain_id: null,
    need_proxy: false,
    proxy_name: '',
    description: asset.description || ''
  }

  // 触发连接事件，复用现有的SSH连接逻辑
  eventBus.emit('currentClickServer', assetNode)
  console.log('准备SSH连接到:', assetNode)
}

// Console连接处理
const handleConsoleConnect = async (asset: EnterpriseAsset) => {
  try {
    console.log('准备Console连接到:', asset)

    // 构建Console连接配置
    const consoleConfig = {
      assetId: asset.id,
      type: asset.console_type || 'telnet', // 默认使用telnet
      config: {
        host: asset.host,
        port: asset.port,
        username: asset.username,
        password: asset.password,
        console_type: asset.console_type || 'telnet',
        serial_config: asset.serial_config || {
          baudRate: 9600,
          dataBits: 8,
          stopBits: 1,
          parity: 'none',
          device: ''
        },
        telnet_config: asset.telnet_config || {
          timeout: 30
        }
      }
    }

    // 调用Console连接API
    const response = await window.electronAPI?.consoleConnect(consoleConfig)

    if (response?.success) {
      message.success(`Console连接成功: ${asset.name}`)
      // 可以在这里打开Console终端窗口
      eventBus.emit('openConsoleTerminal', {
        connectionId: response.connectionId,
        assetName: asset.name,
        assetId: asset.id
      })
    } else {
      throw new Error(response?.message || 'Console连接失败')
    }
  } catch (error: any) {
    console.error('Console连接失败:', error)
    message.error(`Console连接失败: ${error.message}`)
  }
}

// RDP连接处理
const handleRdpConnect = async (asset: EnterpriseAsset) => {
  try {
    console.log('准备RDP连接到:', asset)

    // 构建RDP连接配置
    const rdpConfig = {
      assetId: asset.id,
      config: {
        host: asset.host,
        port: asset.port || 3389,
        username: asset.username,
        password: asset.password,
        domain: asset.rdp_config?.domain || 'WORKGROUP'
      },
      displaySettings: asset.rdp_config?.display_settings || {
        width: 1920,
        height: 1080,
        colorDepth: 32
      }
    }

    // 调用RDP连接API
    const response = await window.electronAPI?.rdpConnect(rdpConfig)

    if (response?.success) {
      message.success(`RDP连接成功: ${asset.name}`)
      // 可以在这里打开RDP窗口
      eventBus.emit('openRdpWindow', {
        connectionId: response.connectionId,
        assetName: asset.name,
        assetId: asset.id
      })
    } else {
      throw new Error(response?.message || 'RDP连接失败')
    }
  } catch (error: any) {
    console.error('RDP连接失败:', error)
    message.error(`RDP连接失败: ${error.message}`)
  }
}

const handleAssetEdit = (asset: EnterpriseAsset) => {
  isEditMode.value = true
  editingAssetId.value = asset.id
  // 填充表单数据
  formData.value = { ...asset }
  isModalVisible.value = true
}

const handleNewAsset = () => {
  isEditMode.value = false
  editingAssetId.value = null
  resetForm()
  isModalVisible.value = true
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: '',
    name: '',
    host: '',
    port: 22,
    type: 'server',
    status: 'unknown',
    organization: '',
    department: '',
    businessType: '',
    region: '',
    environment: '',
    username: '',
    password: '',
    description: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 表单提交
const handleFormSubmit = async () => {
  try {
    await formRef.value.validate()

    if (isEditMode.value && editingAssetId.value) {
      // 编辑模式
      const index = assets.value.findIndex((asset) => asset.id === editingAssetId.value)
      if (index !== -1) {
        assets.value[index] = { ...formData.value, id: editingAssetId.value }
        console.log('企业资产更新成功:', formData.value)
      }
    } else {
      // 新建模式
      const newAsset = {
        ...formData.value,
        id: Date.now().toString() // 简单的ID生成
      }
      assets.value.push(newAsset)
      console.log('企业资产创建成功:', newAsset)
    }

    isModalVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消表单
const handleModalCancel = () => {
  isModalVisible.value = false
  resetForm()
}

const handleImportAssets = () => {
  console.log('导入企业资产')
}

const handleExportAssets = () => {
  console.log('导出企业资产')
}

const getAssetTypeLabel = (type: string) => {
  const labels = {
    server: '服务器',
    database: '数据库',
    network: '网络设备',
    console: 'Console设备',
    rdp: 'RDP服务器'
  }
  return labels[type] || type
}

const getStatusLabel = (status: string) => {
  const labels = {
    online: '在线',
    offline: '离线',
    unknown: '未知'
  }
  return labels[status] || status
}

// 初始化企业资产数据到数据库
const initializeEnterpriseAssetsToDatabase = async () => {
  if (typeof window === 'undefined' || !window.api) {
    console.log('非 Electron 环境，跳过数据库初始化')
    return
  }

  try {
    console.log('检查是否需要初始化企业资产数据...')

    // 先检查数据库中是否已有企业资产数据
    const api = window.api as any
    const res = await api.getLocalAssetRoute({ searchType: 'tree', params: ['organization'] })
    if (res && res.success && res.data && res.data.data && res.data.data.routers && res.data.data.routers.length > 0) {
      console.log('数据库中已有企业资产数据，跳过初始化')
      return
    }

    console.log('开始初始化企业资产数据到数据库...')

    // 创建企业资产管理组织
    const orgForm = {
      label: '企业资产管理',
      ip: '***********',
      port: 22,
      username: 'admin',
      password: '',
      asset_type: 'organization',
      auth_type: 'password',
      keyChain: 0,
      group_name: '企业资产管理',
      needProxy: false,
      proxyName: ''
    }

    // 使用现有的 asset-create API
    const orgResult = await api.createAsset({ form: orgForm })
    if (orgResult && orgResult.success) {
      console.log('企业资产管理组织已添加到数据库')
    } else {
      console.error('创建企业资产管理组织失败:', orgResult)
    }

    console.log('企业资产数据初始化完成')
  } catch (error) {
    console.error('初始化企业资产数据失败:', error)
  }
}

// 组件生命周期
const handleClickOutside = (event: MouseEvent) => {
  if (contextMenuVisible.value) {
    closeContextMenu()
  }
}

onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  // 初始化企业资产数据到数据库（仅在第一次运行时）
  await initializeEnterpriseAssetsToDatabase()
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="less">
.enterprise-asset-config {
  padding: 20px;
  background: var(--bg-color);
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 20px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: var(--text-color-tertiary);
    font-size: 14px;
  }
}

.search-section {
  margin-bottom: 20px;
}

.search-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  flex: 1;
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);

  :deep(.ant-input) {
    background: var(--bg-color-secondary);
    color: var(--text-color);
    border: none;

    &::placeholder {
      color: var(--text-color-tertiary);
    }
  }

  :deep(.ant-input-suffix) {
    color: var(--text-color-tertiary);
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

// 过滤器样式
.filter-section {
  margin-bottom: 16px;
}

.filter-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  padding: 12px 16px;
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 12px;
  color: var(--text-color-secondary);
  white-space: nowrap;
}

// 分组样式
.grouped-assets {
  .asset-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    margin-bottom: 12px;
  }

  .group-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 18px;
      color: var(--primary-color);
    }
  }

  .group-count {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-color-tertiary);
  }
}

.assets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;

  // 响应式布局
  @media (min-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
  }

  @media (min-width: 1600px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
  }

  @media (min-width: 2000px) {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }
}

.asset-card {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 80px;
  max-height: 100px;
  position: relative;
  display: flex;
  flex-direction: column;

  &:hover {
    border-color: var(--primary-color);

    .asset-edit {
      opacity: 1;
    }
  }

  // 响应式调整
  @media (min-width: 1600px) {
    padding: 6px;
    min-height: 75px;
    max-height: 95px;
  }
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.asset-icon {
  font-size: 20px;
  color: var(--primary-color);
}

.asset-edit {
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;

  &:hover {
    background: var(--bg-color-hover);
  }
}

.asset-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .asset-name {
    margin: 0 0 2px 0;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;

    @media (min-width: 1600px) {
      font-size: 11px;
    }
  }

  .asset-address {
    margin: 0 0 3px 0;
    font-size: 10px;
    color: var(--text-color-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;

    @media (min-width: 1600px) {
      font-size: 9px;
    }
  }

  .asset-meta {
    display: flex;
    gap: 4px;
    margin-bottom: 2px;
    flex-wrap: wrap;
  }

  .asset-type {
    font-size: 9px;
    padding: 1px 4px;
    background: var(--bg-color-hover);
    color: var(--text-color-secondary);
    border-radius: 2px;
    white-space: nowrap;

    @media (min-width: 1600px) {
      font-size: 8px;
      padding: 1px 3px;
    }
  }

  .asset-status {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 2px;
    white-space: nowrap;

    @media (min-width: 1600px) {
      font-size: 8px;
      padding: 1px 3px;
    }

    &.online {
      background: #d1fae5;
      color: #065f46;
    }

    &.offline {
      background: #fee2e2;
      color: #991b1b;
    }

    &.unknown {
      background: #f3f4f6;
      color: #374151;
    }
  }

  .asset-org {
    margin: 0;
    font-size: 9px;
    color: var(--text-color-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;

    @media (min-width: 1600px) {
      font-size: 8px;
    }
  }

  .asset-tags {
    display: flex;
    gap: 2px;
    margin-top: 2px;
    flex-wrap: wrap;
  }

  .asset-tag {
    font-size: 8px;
    padding: 1px 3px;
    background: var(--bg-color-hover);
    color: var(--text-color-tertiary);
    border-radius: 2px;
    white-space: nowrap;
    line-height: 1.2;

    @media (min-width: 1600px) {
      font-size: 7px;
      padding: 0px 2px;
    }
  }
}
</style>
